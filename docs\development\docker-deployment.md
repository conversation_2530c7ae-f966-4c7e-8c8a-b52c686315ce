# Docker Deployment Guide

Denna guide beskriver hur du kör RPA-projektet i Docker-containers för både utveckling och produktion.

## 📋 Översikt

RPA-projektet består av tre huvudkomponenter som körs i separata containers:
- **Frontend**: React-applikation serverad via Nginx
- **Backend**: Node.js API-server med Playwright
- **Redis**: Cache och jobbkö för BullMQ

## 🚀 Snabbstart

### 1. Förberedelser

```bash
# Klona projektet
git clone <repository-url>
cd rpa-project

# Kopiera och konfigurera miljövariabler
cp .env.docker.example .env.docker
# Redigera .env.docker med dina värden
```

### 2. Bygg och starta

```bash
# Bygg alla containers
npm run docker:build

# Starta alla tjänster
npm run docker:up

# Visa loggar
npm run docker:logs
```

### 3. Åtkomst

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3002
- **Redis**: localhost:6379

## 🔧 Konfiguration

### Miljövariabler

Kopiera `.env.docker.example` till `.env.docker` och uppdatera följande värden:

#### Obligatoriska inställningar
```bash
# LLM Provider (välj en)
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openai_api_key

# ELLER för Azure
LLM_PROVIDER=azure
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
```

#### OAuth2 för externa integrationer
```bash
# eEkonomi (Visma)
EEKONOMI_CLIENT_ID=your_client_id
EEKONOMI_CLIENT_SECRET=your_client_secret

# Fortnox
FORTNOX_CLIENT_ID=your_client_id
FORTNOX_CLIENT_SECRET=your_client_secret
```

### Portkonfiguration

Standard portar:
- Frontend: `3000` → `80` (container)
- Backend: `3002` → `3002` (container)
- Redis: `6379` → `6379` (container)

För att ändra externa portar, uppdatera `docker-compose.yml`:
```yaml
ports:
  - "8080:80"  # Frontend på port 8080
  - "8002:3002"  # Backend på port 8002
```

## 🏗️ Arkitektur

### Container-struktur

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │     Redis       │
│   (Nginx)       │    │   (Node.js)     │    │   (Cache/Queue) │
│   Port: 80      │    │   Port: 3002    │    │   Port: 6379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                        ┌─────────────────┐
                        │  rpa-network    │
                        │   (Bridge)      │
                        └─────────────────┘
```

### Datavolumer

- `redis_data`: Persistent Redis-data
- `./backend/data`: SQLite-databas och applikationsdata (bind mount för utveckling)
- `./screenshots`: Skärmdumpar från RPA-körningar
- `./downloads`: Nedladdade filer

#### Databas-volym konfiguration

**Nuvarande konfiguration (Utveckling):**
```yaml
volumes:
  - ./backend/data:/app/data  # Bind mount - direkt mappning till lokal mapp
```

**Fördelar:**
- Enkel åtkomst till databasfiler från host-systemet
- Perfekt för utveckling och debugging
- Data delas mellan lokal utveckling och Docker

**⚠️ Produktionsnotering:**
För produktionsmiljöer bör du överväga att använda Docker-hanterade volymer istället för bind mounts av säkerhets- och prestandaskäl:

```yaml
# Rekommenderad produktionskonfiguration
volumes:
  - backend_data:/app/data  # Docker-hanterad volym

volumes:
  backend_data:
    driver: local
```

**Fördelar med Docker-volymer i produktion:**
- Bättre prestanda (speciellt på Windows/Mac)
- Säkrare - data är isolerad från host-systemet
- Automatisk hantering av filrättigheter
- Enklare backup-strategier med Docker-verktyg

## 🛠️ Utveckling

### Development mode

För utveckling med hot-reload:

```bash
# Starta endast Redis i Docker
docker-compose up redis -d

# Kör frontend och backend lokalt
npm run dev
```

### Debugging

```bash
# Visa loggar för specifik tjänst
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f redis

# Kör kommandon i container
docker-compose exec backend bash
docker-compose exec frontend sh

# Kontrollera hälsostatus
docker-compose ps
```

### Rebuild efter ändringar

```bash
# Bygg om specifik tjänst
docker-compose build backend
docker-compose build frontend

# Bygg om allt
docker-compose build --no-cache
```

## 🚀 Produktion

### Databasvolym för produktion

När du går från utveckling till produktion, överväg att migrera från bind mount till Docker-volym:

#### 1. Backup av befintlig data
```bash
# Stoppa containers
docker-compose down

# Skapa backup av befintlig databas
cp -r ./backend/data ./backend/data.backup
```

#### 2. Uppdatera docker-compose.yml
```yaml
# Ändra från:
volumes:
  - ./backend/data:/app/data

# Till:
volumes:
  - backend_data:/app/data

# Lägg till volym-definition:
volumes:
  backend_data:
    driver: local
```

#### 3. Migrera data till Docker-volym
```bash
# Starta med ny volym-konfiguration
docker-compose up -d

# Kopiera data till den nya volymen
docker cp ./backend/data/. rpa-backend:/app/data/

# Starta om backend för att säkerställa att allt fungerar
docker-compose restart backend
```

#### 4. Verifiera migrering
```bash
# Kontrollera att data finns i containern
docker-compose exec backend ls -la /app/data/

# Testa applikationen för att säkerställa att all data är tillgänglig
curl http://localhost:3002/api/customers
```

### Säkerhetsförbättringar

1. **Uppdatera OAuth2 redirect URIs**:
```bash
# I .env.docker
EEKONOMI_REDIRECT_URI=https://yourdomain.com/api/oauth2/callback/eEkonomi
FORTNOX_REDIRECT_URI=https://yourdomain.com/api/oauth2/callback/Fortnox
```

2. **Sätt CORS origin**:
```bash
CORS_ORIGIN=https://yourdomain.com
```

3. **Använd Docker secrets** för känsliga värden:
```yaml
# docker-compose.prod.yml
secrets:
  openai_api_key:
    file: ./secrets/openai_api_key.txt
```

### SSL/TLS

För HTTPS, använd en reverse proxy som Nginx eller Traefik:

```yaml
# docker-compose.prod.yml
services:
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
```

### Monitoring

Lägg till monitoring med Docker Compose:

```yaml
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
  
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
```

## 🔍 Felsökning

### Vanliga problem

#### 1. Port redan används
```bash
Error: bind: address already in use
```
**Lösning**: Stoppa tjänster som använder portarna eller ändra portar i docker-compose.yml

#### 2. Permission denied
```bash
Error: permission denied
```
**Lösning**: Kontrollera filrättigheter för volymer
```bash
sudo chown -R $USER:$USER ./screenshots ./downloads
```

#### 3. Container startar inte
```bash
# Kontrollera loggar
docker-compose logs backend

# Kontrollera hälsostatus
docker-compose ps
```

#### 4. API-anrop misslyckas
- Kontrollera att backend är tillgänglig: `curl http://localhost:3002/health`
- Verifiera nätverkskonfiguration
- Kontrollera miljövariabler

### Prestandaoptimering

```bash
# Begränsa resurser
docker-compose up --scale backend=2  # Flera backend-instanser
```

```yaml
# I docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

## 📚 Kommandon

### Docker Compose

```bash
# Grundläggande
npm run docker:build    # Bygg alla containers
npm run docker:up       # Starta alla tjänster
npm run docker:down     # Stoppa alla tjänster
npm run docker:logs     # Visa loggar

# Avancerat
docker-compose up -d --scale backend=2  # Skala backend
docker-compose restart backend          # Starta om backend
docker-compose pull                     # Uppdatera base images
```

### Underhåll

```bash
# Rensa oanvända containers och images
docker system prune -a

# Backup av databas (bind mount - utveckling)
cp -r ./backend/data ./backup/database-$(date +%Y%m%d-%H%M%S)

# Backup av databas (Docker volym - produktion)
docker run --rm -v rpa-project_backend_data:/data -v $(pwd)/backup:/backup alpine tar czf /backup/database-$(date +%Y%m%d-%H%M%S).tar.gz -C /data .

# Backup av Redis-data
docker run --rm -v rpa-project_redis_data:/data -v $(pwd)/backup:/backup alpine tar czf /backup/redis-$(date +%Y%m%d-%H%M%S).tar.gz -C /data .

# Restore av databas (bind mount)
cp -r ./backup/database-20250716-195000/* ./backend/data/

# Restore av databas (Docker volym)
docker run --rm -v rpa-project_backend_data:/data -v $(pwd)/backup:/backup alpine tar xzf /backup/database-20250716-195000.tar.gz -C /data

# Restore av Redis-data
docker run --rm -v rpa-project_redis_data:/data -v $(pwd)/backup:/backup alpine tar xzf /backup/redis-20250716-195000.tar.gz -C /data
```

## 🔗 Relaterade dokument

- [Architecture](architecture.md) - Systemarkitektur
- [Build System](build-system.md) - Build-process
- [Troubleshooting](troubleshooting.md) - Allmän felsökning
- [LLM Provider Configuration](../user-guide/LLM_PROVIDER_CONFIGURATION.md) - AI-konfiguration
